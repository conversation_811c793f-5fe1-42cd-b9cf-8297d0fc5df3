{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022", "DOM"], "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "outDir": "./dist", "rootDir": "./", "baseUrl": "./", "paths": {"@/*": ["src/*"], "@config/*": ["src/config/*"], "@pages/*": ["src/pages/*"], "@testData/*": ["src/testData/*"], "@tests/*": ["tests/*"]}, "types": ["node", "@playwright/test"], "experimentalDecorators": true, "emitDecoratorMetadata": true, "noEmit": false, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false}, "include": ["src/**/*", "tests/**/*", "playwright.config.ts", "*.ts"], "exclude": ["node_modules", "dist", "generated"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}