"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const test_1 = require("@playwright/test");
const testDataHelpers_1 = require("../../../src/database/testDataHelpers");
const connection_1 = require("../../../src/database/connection");
test_1.test.describe('Pruebas con base de datos', () => {
    test_1.test.beforeAll(async () => {
        await connection_1.database.connect();
    });
    test_1.test.afterAll(async () => {
        await connection_1.database.disconnect();
    });
    (0, test_1.test)('Consulta de usuarios', async () => {
        const users = await (0, testDataHelpers_1.getTrakingsByDate)({ fecha_emision: '2023-10-10', emision: 1, limit: 10 });
        (0, test_1.expect)(users.length).toBeGreaterThan(0);
    });
    test_1.test.afterEach(async () => {
        await connection_1.database.cleanupTestData();
    });
});
//# sourceMappingURL=validacionNormalizacionDirecciones.spec.js.map