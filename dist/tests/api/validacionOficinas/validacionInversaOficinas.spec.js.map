{"version": 3, "file": "validacionInversaOficinas.spec.js", "sourceRoot": "", "sources": ["../../../../tests/api/validacionOficinas/validacionInversaOficinas.spec.ts"], "names": [], "mappings": ";;AAAA,2CAA+C;AAC/C,uDAAmD;AACnD,6CAAiF;AACjF,qDAAuD;AAGvD,IAAI,GAAQ,CAAA;AACZ,MAAM,SAAS,GAAG,sDAAsD,CAAA;AACxE,MAAM,SAAS,GAAG,qBAAqB,CAAA;AACvC,MAAM,oBAAoB,GAAsB,EAAE,CAAA;AAElD,WAAI,CAAC,UAAU,CAAC,KAAK,IAAI,EAAE;IACzB,MAAM,UAAU,GAAG,IAAI,SAAG,EAAE,CAAA;IAC5B,GAAG,GAAG,MAAM,UAAU,CAAC,IAAI,EAAE,CAAA;AAC/B,CAAC,CAAC,CAAA;AAEF,IAAA,WAAI,EAAC,2DAA2D,EAAE,KAAK,IAAI,EAAE;IAC3E,WAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA,CAAC,kFAAkF;IAE1G,MAAM,KAAK,GAAG,IAAA,6BAAmB,EAAC,SAAS,EAAE,SAAS,CAAC,CAAA;IACvD,IAAA,+BAAiB,EAAC,KAAK,EAAE,SAAS,CAAC,CAAA;IAEnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,MAAM,IAAI,GAAQ,KAAK,CAAC,CAAC,CAAC,CAAA;QAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAA;QAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAA;QACxC,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,QAAQ,GAAG,sBAAsB,CAAC,CAAA;YAC/C,SAAQ;QACV,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAA;QACtC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,CAAC,IAAI,CAAC,QAAQ,GAAG,qBAAqB,CAAC,CAAA;YAC9C,SAAQ;QACV,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAA;QAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAA;QACzC,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAA;QACjD,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAA;QAEzC,OAAO,CAAC,GAAG,CAAC,0BAA0B,GAAG,KAAK,SAAS,EAAE,CAAC,CAAA;QAC1D,MAAM,kBAAkB,GAAG,MAAM,GAAG,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAA;QAEvE,IAAA,aAAM,EAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,CAAA;QACzD,IAAI,YAAY,GAAG,IAAI,CAAA;QACvB,IAAI,kBAAkB,CAAC,MAAM,EAAE,KAAK,GAAG,EAAE,CAAC;YACxC,YAAY,GAAG,MAAM,kBAAkB,CAAC,IAAI,EAAE,CAAA;QAChD,CAAC;QAED,MAAM,WAAW,GAAG,CAAC,YAAY,IAAI,CAAC,OAAO,YAAY,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAA;QAEjH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,CAAC,GAAG,CAAC,oCAAoC,GAAG,EAAE,CAAC,CAAA;YACtD,IAAI,CAAC,YAAY,CAAC,OAAO,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,SAAS,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;gBACvG,OAAO,CAAC,IAAI,CAAC,0CAA0C,GAAG,kDAAkD,CAAC,CAAA;gBAC7G,oBAAoB,CAAC,IAAI,CAAC;oBACxB,GAAG;oBACH,gBAAgB,EAAE,SAAS;oBAC3B,iBAAiB,EAAE,gBAAgB;oBACnC,eAAe,EAAE,QAAQ;oBACzB,gBAAgB,EAAE,gBAAgB;oBAClC,gBAAgB,EAAE,SAAS;oBAC3B,gBAAgB,EAAE,SAAS;oBAC3B,iBAAiB,EAAE,gBAAgB;oBACnC,SAAS,EAAE,OAAO;oBAClB,SAAS,EAAE,SAAS;oBACpB,aAAa,EAAE,aAAa;iBAC7B,CAAC,CAAA;gBACF,SAAQ;YACV,CAAC;YAED,MAAM,iBAAiB,GAAG,YAAY,CAAC,MAAM,CAAA;YAC7C,MAAM,EAAE,SAAS,EAAE,iBAAiB,EAAE,QAAQ,EAAE,gBAAgB,EAAE,GAAG,YAAY,CAAC,WAAW,CAAA;YAC7F,MAAM,SAAS,GAAG,YAAY,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAA;YAEhD,oBAAoB,CAAC,IAAI,CAAC;gBACxB,GAAG;gBACH,gBAAgB,EAAE,SAAS;gBAC3B,iBAAiB,EAAE,iBAAiB;gBACpC,eAAe,EAAE,QAAQ;gBACzB,gBAAgB,EAAE,gBAAgB;gBAClC,gBAAgB,EAAE,SAAS;gBAC3B,gBAAgB,EAAE,SAAS;gBAC3B,iBAAiB,EAAE,iBAAiB;gBACpC,SAAS,EAAE,SAAS;gBACpB,SAAS,EAAE,SAAS;gBACpB,aAAa,EAAE,aAAa;aAC7B,CAAC,CAAA;QACJ,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,4CAA4C,GAAG,EAAE,CAAC,CAAA;YAC/D,oBAAoB,CAAC,IAAI,CAAC;gBACxB,GAAG;gBACH,gBAAgB,EAAE,SAAS;gBAC3B,iBAAiB,EAAE,gBAAgB;gBACnC,eAAe,EAAE,QAAQ;gBACzB,gBAAgB,EAAE,gBAAgB;gBAClC,gBAAgB,EAAE,SAAS;gBAC3B,gBAAgB,EAAE,SAAS;gBAC3B,iBAAiB,EAAE,gBAAgB;gBACnC,SAAS,EAAE,OAAO;gBAClB,SAAS,EAAE,SAAS;gBACpB,aAAa,EAAE,aAAa;aAC7B,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED,MAAM,cAAc,GAAG,oBAAoB,CAAC,MAAM,CAAA;IAClD,MAAM,QAAQ,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,MAAM,CAAA;IACxF,MAAM,QAAQ,GAAG,cAAc,GAAG,QAAQ,CAAA;IAE1C,OAAO,CAAC,GAAG,CAAC,eAAe,cAAc,gBAAgB,QAAQ,sCAAsC,QAAQ,iCAAiC,CAAC,CAAA;IACjJ,OAAO,CAAC,GAAG,CAAC,OAAO,QAAQ,OAAO,cAAc,+CAA+C,CAAC,CAAA;IAChG,sBAAsB;IACtB,IAAA,oCAA0B,EAAkB;QAC1C,IAAI,EAAE,oBAAoB;QAC1B,UAAU,EAAE,uCAAuC;QACnD,OAAO,EAAE;YACP,KAAK;YACL,kBAAkB;YAClB,mBAAmB;YACnB,iBAAiB;YACjB,kBAAkB;YAClB,aAAa;YACb,mBAAmB;YACnB,oBAAoB;YACpB,aAAa;YACb,YAAY;YACZ,gBAAgB;SACjB;QACD,aAAa,EAAE;YACb,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG;YACZ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gBAAgB;YACzB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,iBAAiB;YAC1B,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe;YACxB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gBAAgB;YACzB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gBAAgB;YACzB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gBAAgB;YACzB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,iBAAiB;YAC1B,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS;YAClB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS;YAClB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa;SACvB;KACF,CAAC,CAAA;IACF,IAAA,aAAM,EAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;AACvC,CAAC,CAAC,CAAA"}