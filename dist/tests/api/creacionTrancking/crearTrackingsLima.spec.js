"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const test_1 = require("@playwright/test");
const envioRest_1 = require("@/apiProviders/envioRest");
const crearEnvioBody_json_1 = __importDefault(require("@/testData/archivosJson/crearEnvioBody.json"));
const ciudades_1 = require("@/config/ciudades");
const helpers_1 = require("@/utils/helpers");
const validadores_1 = require("@/utils/validadores");
let envioRest;
// Configura tu base para códigoOptitrack
const baseCodigoOptitrack = 50000; // puedes cambiar este valor
// Obtener los IDs de la ciudad actual
const ciudadActual = 'Lima';
const idSede = ciudades_1.typeCiudad[ciudadActual].idSede;
const idOficina = ciudades_1.typeCiudad[ciudadActual].idOficina;
// Ruta y nombre de la hoja de Excel
const excelPath = './src/testData/archivosExcel/DireccionesTrackingUbigeosTodosLosCasos.xlsx';
const sheetName = 'DireccionesUbigeoPrimero';
// 🧪 Setup antes de cada test
test_1.test.beforeEach(async () => {
    const currentEnvioRest = new envioRest_1.EnvioRest();
    envioRest = await currentEnvioRest.init();
});
// 🧪 Test principal con múltiples envíos
(0, test_1.test)('Crear trackings en la sede de Lima, 1 request por body (Iterativo)', async () => {
    // Paso 1: Login
    const loginResponse = await envioRest.postLogin('olvati', 'J&_Mv9]H^2Vx');
    (0, test_1.expect)(loginResponse.status()).toBe(200);
    const authBody = await loginResponse.json();
    const token = authBody.token;
    (0, test_1.expect)(token).toBeDefined();
    console.log(`🔐 Token obtenido: ${token}`);
    // Paso 2: Leer Excel
    const datos = (0, helpers_1.leerDatosDesdeExcel)(excelPath, sheetName);
    // Validar que el archivo de datos existe y tiene datos
    (0, validadores_1.validarDatosExcel)(datos, sheetName);
    for (let i = 0; i < datos.length; i++) {
        const fila = datos[i];
        const direccion = fila['DIRECCIONES'];
        const codUbigeo = fila['IDUBIGEO'].toString().padStart(6, '0');
        (0, test_1.expect)(codUbigeo.length).toBe(6);
        const idUbigeo = parseInt(codUbigeo) || 0;
        const codigoOptitrack = baseCodigoOptitrack + i - 1;
        // Puedes ajustar los demás parámetros si quieres que también vengan del Excel
        const crearEnvioResponse = await envioRest.postCrearEnvio(token, codigoOptitrack, idSede, idOficina, direccion, 'Pruebas Alem Origen Lima', idUbigeo);
        (0, test_1.expect)(crearEnvioResponse.status()).toBe(201);
        const crearEnvioBody = await crearEnvioResponse.json();
        (0, test_1.expect)('estado' in crearEnvioBody).toBe(true);
        (0, test_1.expect)('remito' in crearEnvioBody).toBe(true);
        console.log(`✅ [${i + 1}] Envío creado: remito=${crearEnvioBody.remito}, emision=${crearEnvioBody.emision}, dirección="${direccion}"`);
    }
});
(0, test_1.test)('Crear trackings en la sede Lima en una sola petición (batch)', async () => {
    // Paso 1: Login
    const loginResponse = await envioRest.postLogin('olvati', 'J&_Mv9]H^2Vx');
    (0, test_1.expect)(loginResponse.status()).toBe(200);
    const authBody = await loginResponse.json();
    const token = authBody.token;
    (0, test_1.expect)(token).toBeDefined();
    console.log(`🔐 Token obtenido: ${token}`);
    // Paso 2: Leer Excel
    const datos = (0, helpers_1.leerDatosDesdeExcel)(excelPath, sheetName);
    // Paso 3: Crear array de envíos
    const listaEnvios = datos.map((fila, i) => {
        const body = structuredClone(crearEnvioBody_json_1.default);
        body.codigoOptitrack = baseCodigoOptitrack + i - 1;
        body.idSede = idSede;
        body.idOficina = idOficina;
        body.direccionEntrega = fila['DIRECCIONES'];
        body.consignado = 'Pruebas Alem Origen Lima';
        const codUbigeo = fila['IDUBIGEO'].toString().padStart(6, '0');
        (0, test_1.expect)(codUbigeo.length).toBe(6);
        body.idUbigeo = parseInt(codUbigeo) || 0;
        return body;
    });
    // Paso 4: Enviar todos los envíos en un solo POST
    const response = await envioRest.postCrearMultiplesEnvios(token, listaEnvios);
    (0, test_1.expect)(response.status()).toBe(201);
    const responseBody = await response.json();
    console.log('📦 Respuesta del envío múltiple:', responseBody);
    // Validar por ejemplo que venga una lista de respuestas
    (0, test_1.expect)(Array.isArray(responseBody)).toBe(true);
    responseBody.forEach((envio, idx) => {
        console.log(`🔖 Envío #${idx + 1}: emision = ${envio.emision}, remito = ${envio.remito}`);
    });
});
//# sourceMappingURL=crearTrackingsLima.spec.js.map