"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const test_1 = require("@playwright/test");
const envioRest_1 = require("@/apiProviders/envioRest");
const ciudades_1 = require("@/config/ciudades");
let envioRest;
const codigoOptitrack = 50000; // editar deacuerdo a lo que se necesita
// Obtener los IDs de la ciudad actual
const ciudadActual = 'Lima';
const idSede = ciudades_1.typeCiudad[ciudadActual].idSede;
const idOficina = ciudades_1.typeCiudad[ciudadActual].idOficina;
// Setup de provider before all test
test_1.test.beforeEach(async () => {
    const currentEnvioRest = new envioRest_1.EnvioRest();
    envioRest = await currentEnvioRest.init();
});
(0, test_1.test)('Crear un tracking', async () => {
    // Paso 1: Login para obtener el token
    const loginResponse = await envioRest.postLogin('olvati', 'J&_Mv9]H^2Vx'); // ver la forma de guardar estas credenciales
    (0, test_1.expect)(loginResponse.status()).toBe(200);
    const authBody = await loginResponse.json();
    const token = authBody.token;
    (0, test_1.expect)(token).toBeDefined();
    console.log(`🔐 Token obtenido: ${token}`);
    // Paso 2: Crear Envío usando el token
    const crearEnvioResponse = await envioRest.postCrearEnvio(token, codigoOptitrack, idSede, idOficina, 'LA PAZ MZ 246 LT 13, YARINACOCHA, CORONEL PORTILLO, UCAYALI', 'TIENDA OECHSLE - CUSCO   ANDREA SILVA  DIGNA CONDORI', 1648);
    (0, test_1.expect)(crearEnvioResponse.status()).toBe(201);
    const crearEnvioBody = await crearEnvioResponse.json();
    (0, test_1.expect)('estado' in crearEnvioBody).toBe(true);
    (0, test_1.expect)('remito' in crearEnvioBody).toBe(true);
    // Paso 3: Mostrar emision y tracking
    console.log(`✅ El envío fue creado correctamente: emision = ${crearEnvioBody.emision}, remito = ${crearEnvioBody.remito}`);
});
//# sourceMappingURL=crearUnTraking.spec.js.map