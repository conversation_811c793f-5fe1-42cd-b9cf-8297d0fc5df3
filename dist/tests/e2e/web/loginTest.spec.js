"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const test_1 = require("@playwright/test");
const environment_1 = require("../../../src/config/environment");
const LoginPage_1 = require("../../../src/pages/LoginPage");
/**
 * Simple Login Test Suite (without database)
 * Tests basic login functionality using Page Object Model
 */
test_1.test.describe('Simple Login Tests', () => {
    let loginPage;
    // Setup before each test
    test_1.test.beforeEach(async ({ page }) => {
        loginPage = new LoginPage_1.LoginPage(page);
        // Navigate to login page
        await loginPage.navigateToLoginPage();
        // Validate login page is loaded
        await loginPage.validateLoginPageLoaded();
    });
    (0, test_1.test)('should successfully login with valid credentials', async () => {
        // Act - Perform login with hardcoded valid credentials
        await loginPage.login(environment_1.environment.test.username, environment_1.environment.test.password);
        // Assert - Verify successful login
        await loginPage.validateSuccessfulLogin();
        // Additional assertions
        const isLoginSuccessful = await loginPage.isLoginSuccessful();
        (0, test_1.expect)(isLoginSuccessful).toBe(true);
        const successMessage = await loginPage.getSuccessMessage();
        (0, test_1.expect)(successMessage).toContain('Logged In Successfully');
        const isLogoutButtonVisible = await loginPage.isLogoutButtonVisible();
        (0, test_1.expect)(isLogoutButtonVisible).toBe(true);
    });
    (0, test_1.test)('should fail login with invalid credentials', async () => {
        // Act - Perform login with hardcoded invalid credentials
        await loginPage.login('invalid_user', 'invalid_password');
        // Assert - Verify failed login
        await loginPage.validateFailedLogin();
    });
});
//# sourceMappingURL=loginTest.spec.js.map