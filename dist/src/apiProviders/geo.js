"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Geo = void 0;
const test_1 = require("@playwright/test");
const environment_1 = require("@config/environment");
class Geo {
    baseUrl;
    async init() {
        this.baseUrl = await test_1.request.newContext({
            extraHTTPHeaders: {
                'Content-Type': 'application/json',
                'x-api-key': environment_1.environment.geoXApiKey
            },
            baseURL: environment_1.environment.apiBaseUrlGeoDev
        });
        return this;
    }
    async getGeoCode(address, ubigeo) {
        const geoCodeResponse = await this.baseUrl.get('/api/v2/geo/code', {
            params: {
                address,
                ubigeo
            }
        });
        return geoCodeResponse;
    }
    async getGeoReverse(lon, lat) {
        const geoReverseResponse = await this.baseUrl.get('/api/v2/geo/reverse', {
            params: {
                lon,
                lat
            }
        });
        return geoReverseResponse;
    }
    async getGeoCodeId(addressId) {
        const geoCodeIdResponse = await this.baseUrl.get('/api/v2/geo/code', {
            params: {
                addressId
            }
        });
        return geoCodeIdResponse;
    }
}
exports.Geo = Geo;
//# sourceMappingURL=geo.js.map