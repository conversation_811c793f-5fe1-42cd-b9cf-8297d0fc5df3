"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnvioRest = void 0;
const test_1 = require("@playwright/test");
const environment_1 = require("@config/environment");
const crearEnvioBody_json_1 = __importDefault(require("@/testData/archivosJson/crearEnvioBody.json"));
class EnvioRest {
    baseUrl;
    async init() {
        this.baseUrl = await test_1.request.newContext({
            extraHTTPHeaders: {
                'Content-Type': 'application/json'
            },
            baseURL: environment_1.environment.apiBaseUrlEnvioRestDev
        });
        return this;
    }
    async postLogin(usuario, clave) {
        return await this.baseUrl.get('/usuario/login', {
            data: {
                usuario,
                clave
            }
        });
    }
    async postCrearEnvio(token, codigoOptitrack, idSede, idOficina, direccion, consignado, idUbigeo) {
        // Clonar el JSON para evitar mutaciones globales
        const body = { ...crearEnvioBody_json_1.default };
        // Sobrescribir solo los campos necesarios
        body.codigoOptitrack = codigoOptitrack;
        body.idSede = idSede;
        body.idOficina = idOficina;
        body.direccionEntrega = direccion;
        body.consignado = consignado;
        body.idUbigeo = idUbigeo;
        const getResponse = await this.baseUrl.get('/envio/crear', {
            headers: {
                Authorization: `Bearer ${token}`
            },
            data: body
        });
        return getResponse;
    }
    async postCrearMultiplesEnvios(token, listaDeEnvios) {
        return await this.baseUrl.post('/envio/crear', {
            data: listaDeEnvios,
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
    }
}
exports.EnvioRest = EnvioRest;
//# sourceMappingURL=envioRest.js.map