"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.prisma = exports.database = void 0;
const client_1 = require("@prisma/client");
const environment_1 = require("@/config/environment");
/**
 * Database connection singleton
 * Manages the Prisma client instance for database operations
 */
class DatabaseConnection {
    static instance;
    prisma;
    constructor() {
        this.prisma = new client_1.PrismaClient({
            log: environment_1.environment.environment === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
            datasources: {
                db: {
                    url: environment_1.environment.database.url
                }
            }
        });
    }
    /**
     * Get the singleton instance of DatabaseConnection
     */
    static getInstance() {
        if (!DatabaseConnection.instance) {
            DatabaseConnection.instance = new DatabaseConnection();
        }
        return DatabaseConnection.instance;
    }
    /**
     * Get the Prisma client instance
     */
    getClient() {
        return this.prisma;
    }
    /**
     * Connect to the database
     */
    async connect() {
        try {
            await this.prisma.$connect();
            console.log('✅ Database connected successfully');
        }
        catch (error) {
            console.error('❌ Database connection failed:', error);
            throw error;
        }
    }
    /**
     * Disconnect from the database
     */
    async disconnect() {
        try {
            await this.prisma.$disconnect();
            console.log('✅ Database disconnected successfully');
        }
        catch (error) {
            console.error('❌ Database disconnection failed:', error);
            throw error;
        }
    }
    /**
     * Check database connection health
     */
    async healthCheck() {
        try {
            await this.prisma.$queryRaw `SELECT 1`;
            return true;
        }
        catch (error) {
            console.error('❌ Database health check failed:', error);
            return false;
        }
    }
    /**
     * Clean up test data (useful for test teardown)
     */
    async cleanupTestData() {
        if (environment_1.environment.environment !== 'test') {
            throw new Error('Cleanup is only allowed in test environment');
        }
        try {
            // Delete all OlvaTrackings records
            await this.prisma.olvaTrackings.deleteMany();
            console.log('✅ Test data cleaned up successfully');
        }
        catch (error) {
            console.error('❌ Test data cleanup failed:', error);
            throw error;
        }
    }
}
// Export singleton instance
exports.database = DatabaseConnection.getInstance();
exports.prisma = exports.database.getClient();
//# sourceMappingURL=connection.js.map