"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTrakingsByDate = getTrakingsByDate;
const connection_1 = require("./connection");
/**
 * Get all test users by role
 */
async function getTrakingsByDate(trackingData) {
    try {
        return await connection_1.prisma.olvaTrackings.findMany({
            where: {
                fecha_emision: trackingData.fecha_emision,
                emision: trackingData.emision
            },
            orderBy: { tracking: 'desc' },
            take: trackingData.limit
        });
    }
    catch (error) {
        console.error('Error fetching trackings by date:', error);
        return [];
    }
}
//# sourceMappingURL=testDataHelpers.js.map