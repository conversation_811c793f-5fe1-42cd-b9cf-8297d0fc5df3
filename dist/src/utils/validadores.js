"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validarDatosExcel = validarDatosExcel;
const test_1 = require("@playwright/test");
/**
 * Valida que una lista de datos leída desde un archivo Excel no esté vacía.
 */
function validarDatosExcel(datos, sheetName) {
    (0, test_1.expect)(datos).not.toBeNull();
    (0, test_1.expect)(Array.isArray(datos)).toBe(true);
    (0, test_1.expect)(datos.length).toBeGreaterThan(0);
    console.log(`✅ Excel '${sheetName}' tiene ${datos.length} registros.`);
}
//# sourceMappingURL=validadores.js.map