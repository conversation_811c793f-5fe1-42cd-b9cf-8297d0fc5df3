"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseNumber = parseNumber;
exports.parseBoolean = parseBoolean;
exports.leerDatosDesdeExcel = leerDatosDesdeExcel;
exports.exportarResultadosGenerico = exportarResultadosGenerico;
const XLSX = __importStar(require("xlsx"));
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
function parseNumber(value, defaultValue) {
    const parsed = Number(value);
    return isNaN(parsed) ? defaultValue : parsed;
}
function parseBoolean(value, defaultValue) {
    return value === 'true' || value === '1' || value === 'on' ? true : value === 'false' || value === '0' || value === 'off' ? false : defaultValue;
}
// Función para leer Excel
function leerDatosDesdeExcel(path, sheet) {
    const workbook = XLSX.readFile(path);
    const hoja = workbook.Sheets[sheet];
    return XLSX.utils.sheet_to_json(hoja);
}
function getTimestamp() {
    const now = new Date();
    const yyyy = now.getFullYear();
    const MM = String(now.getMonth() + 1).padStart(2, '0');
    const dd = String(now.getDate()).padStart(2, '0');
    const hh = String(now.getHours()).padStart(2, '0');
    const mm = String(now.getMinutes()).padStart(2, '0');
    const ss = String(now.getSeconds()).padStart(2, '0');
    return `${yyyy}${MM}${dd}_${hh}${mm}${ss}`;
}
function exportarResultadosGenerico(config) {
    const { data, nombreBase, headers, extraerCampos, nombreHoja = 'Resultados' } = config;
    // 1. Crear libro y hoja
    const libro = XLSX.utils.book_new();
    const hoja = XLSX.utils.aoa_to_sheet([headers]);
    // 2. Filas de datos
    const rows = data.map((item) => extraerCampos.map((fn) => fn(item)));
    XLSX.utils.sheet_add_aoa(hoja, rows, { origin: 'A2' });
    // 3. Ajustar anchos
    hoja['!cols'] = headers.map((h) => ({ wch: Math.max(h.length, 20) }));
    // 4. Adjuntar hoja al libro
    XLSX.utils.book_append_sheet(libro, hoja, nombreHoja);
    // 5. Guardar archivo
    if (!fs.existsSync('resultados-exportados'))
        fs.mkdirSync('resultados-exportados');
    const timestamp = getTimestamp();
    const nombreArchivo = `${nombreBase}_${timestamp}.xlsx`;
    const ruta = path.join('resultados-exportados', nombreArchivo);
    XLSX.writeFile(libro, ruta);
    console.log(`✅ Resultados exportados a: ${ruta}`);
}
//# sourceMappingURL=helpers.js.map