"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoginPage = void 0;
const BasePage_1 = require("./base/BasePage");
/**
 * Login Page Object Model
 * Encapsulates all elements and actions for the login page
 */
class LoginPage extends BasePage_1.BasePage {
    // Constants
    static LOGIN_PATH = '/practice-test-login/';
    static SUCCESS_PATH = '/logged-in-successfully/';
    static MSG_INVALID_USERNAME = 'Your username is invalid!';
    static MSG_SUCCESS_TITLE = 'Logged In Successfully';
    static ASSERT = {
        usernameVisible: 'Username field should be visible',
        passwordVisible: 'Password field should be visible',
        submitVisible: 'Submit button should be visible',
        onLoginPage: 'Should be on login page',
        errorVisible: 'Error message should be visible',
        errorContainsInvalidUser: 'Error message should contain "Your username is invalid!"',
        onSuccessPage: 'Should be on success page',
        successVisible: 'Success message should be visible',
        successContainsTitle: 'Success message should contain "Logged In Successfully"',
        logoutVisible: 'Logout button should be visible'
    };
    // Locators (primary role-based with CSS fallbacks)
    usernameInput;
    passwordInput;
    submitButton;
    pageTitle;
    // Success page elements
    successMessage;
    logoutButton;
    successPageTitle;
    // Error page elements
    errorMessage;
    constructor(page) {
        super(page);
        // Initialize locators (role-based first; chain with CSS fallbacks)
        this.usernameInput = this.page
            .getByLabel(/username/i)
            .or(this.page.locator('#username'));
        this.passwordInput = this.page
            .getByLabel(/password/i)
            .or(this.page.locator('#password'));
        this.submitButton = this.page
            .getByRole('button', { name: /submit|log in|sign in/i })
            .or(this.page.locator('#submit'));
        this.pageTitle = this.page
            .getByRole('heading', { level: 2 })
            .or(this.page.locator('h2'));
        // Success page locators
        this.successMessage = this.page.locator('.post-title');
        this.logoutButton = this.page
            .getByRole('link', { name: /log out/i })
            .or(this.page.locator('a:has-text("Log out")'));
        this.successPageTitle = this.page
            .getByRole('heading', { level: 1 })
            .or(this.page.locator('h1'));
        // Error page locators
        this.errorMessage = this.page
            .getByRole('alert')
            .or(this.page.locator('#error'));
    }
    /**
     * Navigate to login page and wait for it to load
     */
    async navigateToLoginPage() {
        await this.navigateTo(LoginPage.LOGIN_PATH);
        await this.waitForPageLoad();
        await this.validateLoginPageLoaded();
    }
    /**
     * Alternative explicit navigation method
     */
    async goTo() {
        await this.navigateToLoginPage();
    }
    /**
     * Enter username
     */
    async enterUsername(username) {
        await this.waitForElement(this.usernameInput);
        await this.fillInput(this.usernameInput, username);
    }
    /**
     * Enter password
     */
    async enterPassword(password) {
        await this.waitForElement(this.passwordInput);
        await this.fillInput(this.passwordInput, password);
    }
    /**
     * Fill both username and password
     */
    async fillCredentials(username, password) {
        await this.enterUsername(username);
        await this.enterPassword(password);
    }
    /**
     * Click submit button
     */
    async clickSubmit() {
        await this.waitForElement(this.submitButton);
        await this.clickElement(this.submitButton);
    }
    /**
     * Perform complete login flow
     */
    async login(username, password) {
        await this.fillCredentials(username, password);
        await this.clickSubmit();
    }
    // Validation methods
    /**
     * Validate login page is loaded
     */
    async validateLoginPageLoaded() {
        await this.assertElementVisible(this.usernameInput, LoginPage.ASSERT.usernameVisible);
        await this.assertElementVisible(this.passwordInput, LoginPage.ASSERT.passwordVisible);
        await this.assertElementVisible(this.submitButton, LoginPage.ASSERT.submitVisible);
        await this.assertUrlContains(LoginPage.LOGIN_PATH, LoginPage.ASSERT.onLoginPage);
    }
    /**
     * Validate failed login state
     */
    async validateFailedLogin() {
        await this.assertElementVisible(this.errorMessage, LoginPage.ASSERT.errorVisible);
        await this.assertElementContainsText(this.errorMessage, LoginPage.MSG_INVALID_USERNAME, LoginPage.ASSERT.errorContainsInvalidUser);
    }
    /**
     * Validate successful login
     */
    async validateSuccessfulLogin() {
        await this.assertUrlContains(LoginPage.SUCCESS_PATH, LoginPage.ASSERT.onSuccessPage);
        await this.assertElementVisible(this.successMessage, LoginPage.ASSERT.successVisible);
        await this.assertElementContainsText(this.successMessage, LoginPage.MSG_SUCCESS_TITLE, LoginPage.ASSERT.successContainsTitle);
        await this.assertElementVisible(this.logoutButton, LoginPage.ASSERT.logoutVisible);
    }
    // Success page methods
    /**
     * Wait until the success page is reached
     */
    async waitForSuccessPage(timeout = 10000) {
        await this.waitForUrlToContain(LoginPage.SUCCESS_PATH, timeout);
    }
    /**
     * Check if login was successful by verifying URL
     */
    async isLoginSuccessful() {
        try {
            await this.waitForSuccessPage();
            return true;
        }
        catch {
            return false;
        }
    }
    /**
     * Get success message text
     */
    async getSuccessMessage() {
        await this.waitForElement(this.successMessage);
        return await this.getElementText(this.successMessage);
    }
    /**
     * Check if logout button is visible
     */
    async isLogoutButtonVisible() {
        return await this.isElementVisible(this.logoutButton);
    }
    /**
     * Wait for error message after a failed login attempt
     */
    async waitForErrorMessage(timeout = 10000) {
        await this.waitForElement(this.errorMessage, { timeout, state: 'visible' });
    }
    /**
     * Get error message text
     */
    async getErrorMessage() {
        await this.waitForErrorMessage();
        return await this.getElementText(this.errorMessage);
    }
    /**
     * Composite: perform login and assert success
     * Throws with clear message if the success state is not reached.
     */
    async loginAndAssertSuccess(username, password) {
        await this.login(username, password);
        await this.validateSuccessfulLogin();
    }
    /**
     * Composite: perform login and expect failure
     * Optionally accepts a custom expected message.
     */
    async loginAndExpectFailure(username, password, expectedMessage = LoginPage.MSG_INVALID_USERNAME) {
        await this.login(username, password);
        await this.waitForErrorMessage();
        await this.assertElementContainsText(this.errorMessage, expectedMessage, LoginPage.ASSERT.errorContainsInvalidUser);
    }
}
exports.LoginPage = LoginPage;
//# sourceMappingURL=LoginPage.js.map