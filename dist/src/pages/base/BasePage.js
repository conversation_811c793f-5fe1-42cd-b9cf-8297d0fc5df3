"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BasePage = void 0;
const test_1 = require("@playwright/test");
const environment_1 = require("../../config/environment");
class BasePage {
    page;
    baseUrl;
    constructor(page) {
        this.page = page;
        this.baseUrl = environment_1.environment.baseUrl;
    }
    async navigateTo(url) {
        const fullUrl = url.startsWith('http') ? url : `${this.baseUrl}${url}`;
        await this.page.goto(fullUrl, {
            waitUntil: 'domcontentloaded',
            timeout: environment_1.environment.test.timeout
        });
    }
    async waitForPageLoad() {
        await this.page.waitForLoadState('networkidle');
    }
    async assertElementVisible(locator, message) {
        await (0, test_1.expect)(locator, message).toBeVisible();
    }
    async waitForUrlToContain(text, timeout) {
        await this.page.waitForURL((url) => url.toString().includes(text), {
            timeout: timeout || environment_1.environment.test.timeout
        });
    }
    async assertUrlContains(text, message) {
        await (0, test_1.expect)(this.page, message).toHaveURL(new RegExp(text));
    }
    async getElementText(locator) {
        return (await locator.textContent()) || '';
    }
    async isElementVisible(locator) {
        try {
            await locator.waitFor({ state: 'visible', timeout: 5000 });
            return true;
        }
        catch {
            return false;
        }
    }
    async waitForElement(locator, options) {
        const element = typeof locator === 'string' ? this.page.locator(locator) : locator;
        await element.waitFor({
            state: options?.state || 'visible',
            timeout: options?.timeout || environment_1.environment.test.timeout
        });
        return element;
    }
    async fillInput(locator, value) {
        await locator.fill(value);
    }
    async clickElement(locator) {
        await locator.click();
    }
    async assertElementContainsText(locator, expectedText, message) {
        await (0, test_1.expect)(locator, message).toContainText(expectedText);
    }
}
exports.BasePage = BasePage;
//# sourceMappingURL=BasePage.js.map