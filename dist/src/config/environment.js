"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.environment = void 0;
const path_1 = __importDefault(require("path"));
const dotenv_1 = require("dotenv");
const helpers_1 = require("@/utils/helpers");
// Load environment variables from .env file
(0, dotenv_1.config)({ path: path_1.default.resolve(process.cwd(), '.env') });
// ✅ Composición segura de DATABASE_URL
const databaseUrl = `postgresql://${process.env.DB_USER}:${process.env.DB_PASSWORD}@${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_NAME}?schema=${process.env.DB_SCHEMA}`;
/**
 * Environment configuration object
 */
exports.environment = {
    nodeEnv: process.env.NODE_ENV || 'development',
    baseUrl: process.env.BASE_URL, // is obtained from the .env file
    database: {
        url: databaseUrl, // es la composición segura de DB
        host: process.env.DB_HOST || 'localhost', // is obtained from the .env file
        port: (0, helpers_1.parseNumber)(process.env.DB_PORT, 5432),
        name: process.env.DB_NAME || 'qa_automation_db',
        user: process.env.DB_USER || 'username', // is obtained from the .env file
        password: process.env.DB_PASSWORD || 'password', // is obtained from the .env file
        schema: process.env.DB_SCHEMA || 'public',
        pool: {
            min: (0, helpers_1.parseNumber)(process.env.DB_POOL_MIN, 2),
            max: (0, helpers_1.parseNumber)(process.env.DB_POOL_MAX, 10),
            idleTimeout: (0, helpers_1.parseNumber)(process.env.DB_POOL_IDLE_TIMEOUT, 30000)
        }
    },
    test: {
        headless: (0, helpers_1.parseBoolean)(process.env.HEADLESS, true),
        browser: process.env.BROWSER || 'chromium',
        timeout: (0, helpers_1.parseNumber)(process.env.TIMEOUT, 30000),
        retries: (0, helpers_1.parseNumber)(process.env.RETRIES, 2),
        workers: (0, helpers_1.parseNumber)(process.env.WORKERS, 4),
        username: process.env.TEST_USERNAME,
        password: process.env.TEST_PASSWORD
    },
    playwright: {
        storageStatePath: process.env.STORAGE_STATE_PATH || './auth-state.json',
        startLocalServer: (0, helpers_1.parseBoolean)(process.env.START_LOCAL_SERVER, false)
    },
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        file: process.env.LOG_FILE || './logs/test.log'
    },
    media: {
        screenshotMode: process.env.SCREENSHOT_MODE || 'only-on-failure',
        videoMode: process.env.VIDEO_MODE || 'retain-on-failure',
        traceMode: process.env.TRACE_MODE || 'on-first-retry'
    },
    environment: process.env.ENVIRONMENT || 'development', // is obtained from the .env file
    testSuite: process.env.TEST_SUITE || 'smoke',
    apiBaseUrlEnvioRestDev: process.env.API_BASE_URL_ENVIO_REST_DEV || '', // is obtained from the .env file
    apiBaseUrlGeoDev: process.env.API_BASE_URL_GEO_DEV || '',
    apiBaseUrlGeoProd: process.env.API_BASE_URL_GEO_PROD || '',
    geoXApiKey: process.env.GEO_X_API_KEY || ''
};
//# sourceMappingURL=environment.js.map