"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const connection_js_1 = require("./src/database/connection.js");
async function testDatabaseConnection() {
    try {
        console.log('Testing database connection...');
        const isHealthy = await connection_js_1.database.healthCheck();
        console.log('Database health check result:', isHealthy);
    }
    catch (error) {
        console.error('Error testing database connection:', error);
    }
}
testDatabaseConnection();
//# sourceMappingURL=test-connection.js.map