import { test, expect } from '@playwright/test'
import { getTrakingsByDate } from '../../../src/database/testDataHelpers'
import { database } from '../../../src/database/connection'

test.describe('Pruebas con base de datos', () => {
  test.beforeAll(async () => {
    await database.connect()
  })

  test.afterAll(async () => {
    await database.disconnect()
  })

  test('Consulta de usuarios', async () => {
    const users = await getTrakingsByDate({ fecha_emision: '2023-10-10', emision: 1, limit: 10 })
    expect(users.length).toBeGreaterThan(0)
  })

  test.afterEach(async () => {
    await database.cleanupTestData()
  })
})
