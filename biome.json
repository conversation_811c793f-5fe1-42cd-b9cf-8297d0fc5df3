{"$schema": "./node_modules/@biomejs/biome/configuration_schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "formatter": {"enabled": true, "formatWithErrors": false, "ignore": [], "attributePosition": "auto", "indentStyle": "space", "indentWidth": 2, "lineWidth": 150, "lineEnding": "lf"}, "javascript": {"parser": {"unsafeParameterDecoratorsEnabled": true}, "formatter": {"arrowParentheses": "always", "bracketSameLine": false, "bracketSpacing": true, "jsxQuoteStyle": "single", "quoteProperties": "asNeeded", "semicolons": "asNeeded", "trailingCommas": "none", "quoteStyle": "single"}}, "json": {"formatter": {"trailingCommas": "none"}}, "linter": {"enabled": true, "rules": {"recommended": false, "complexity": {"noBannedTypes": "error", "noUselessThisAlias": "error", "noUselessTypeConstraint": "error", "noWith": "error"}, "correctness": {"noPrecisionLoss": "error", "noUnusedVariables": {"level": "error", "fix": "safe"}, "useArrayLiterals": "off"}, "style": {"noNamespace": "error", "noNonNullAssertion": "off", "noYodaExpression": "error", "useAsConstAssertion": "error", "useBlockStatements": "off", "useCollapsedElseIf": "error", "useConst": {"level": "warn", "fix": "safe"}, "useTemplate": {"level": "warn", "fix": "safe"}}, "suspicious": {"noDoubleEquals": "error", "noExplicitAny": "error", "noExtraNonNullAssertion": "error", "noMisleadingInstantiator": "error", "noUnsafeDeclarationMerging": "error"}}, "ignore": ["build/*", "public/*", "**/node_modules", "**/generated", "**/_schema.ts"]}, "overrides": [{"include": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidConstructorSuper": "off", "noNewSymbol": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"noArguments": "error", "noVar": "error"}, "suspicious": {"noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "useGetterReturn": "off"}}}}, {"include": ["**/__tests__/**/*.ts", "**/test/**/*.ts", "**/*.test.ts", "**/*.spec.ts", "**/setup/**/*.ts", "**/utils/**/*.ts"], "linter": {"rules": {"suspicious": {"noExplicitAny": "off"}}}}]}