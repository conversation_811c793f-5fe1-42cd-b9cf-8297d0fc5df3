import { OlvaTrackings } from '@prisma/client'
import { trackingData } from '@/types/dataBaseInterfaces'
import { prisma } from './connection'

/**
 * Get all test users by role
 */
export async function getTrakingsByDate(trackingData: trackingData): Promise<OlvaTrackings[]> {
  try {
    return await prisma.olvaTrackings.findMany({
      where: {
        fecha_emision: trackingData.fecha_emision,
        emision: trackingData.emision
      },
      include: {
        trackingDetails: true,
        addressDetails: true
      },
      orderBy: { tracking: 'desc' },
      take: trackingData.limit
    })
  } catch (error) {
    console.error('Error fetching trackings by date:', error)
    return []
  }
}
