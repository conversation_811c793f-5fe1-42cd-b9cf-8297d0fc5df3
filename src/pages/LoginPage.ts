import { Locator, Page } from '@playwright/test'
import { BasePage } from './base/BasePage'

/**
 * Login Page Object Model
 * Encapsulates all elements and actions for the login page
 */
export class LoginPage extends BasePage {
  // Constants
  private static readonly LOGIN_PATH = '/practice-test-login/'
  private static readonly SUCCESS_PATH = '/logged-in-successfully/'
  private static readonly MSG_INVALID_USERNAME = 'Your username is invalid!'
  private static readonly MSG_SUCCESS_TITLE = 'Logged In Successfully'
  private static readonly ASSERT = {
    usernameVisible: 'Username field should be visible',
    passwordVisible: 'Password field should be visible',
    submitVisible: 'Submit button should be visible',
    onLoginPage: 'Should be on login page',
    errorVisible: 'Error message should be visible',
    errorContainsInvalidUser: 'Error message should contain "Your username is invalid!"',
    onSuccessPage: 'Should be on success page',
    successVisible: 'Success message should be visible',
    successContainsTitle: 'Success message should contain "Logged In Successfully"',
    logoutVisible: 'Logout button should be visible'
  } as const

  // Locators (primary role-based with CSS fallbacks)
  private readonly usernameInput: Locator
  private readonly passwordInput: Locator
  private readonly submitButton: Locator
  private readonly pageTitle: Locator

  // Success page elements
  private readonly successMessage: Locator
  private readonly logoutButton: Locator
  private readonly successPageTitle: Locator

  // Error page elements
  private readonly errorMessage: Locator

  constructor(page: Page) {
    super(page)

    // Initialize locators (role-based first; chain with CSS fallbacks)
    this.usernameInput = this.page
      .getByLabel(/username/i)
      .or(this.page.locator('#username'))

    this.passwordInput = this.page
      .getByLabel(/password/i)
      .or(this.page.locator('#password'))

    this.submitButton = this.page
      .getByRole('button', { name: /submit|log in|sign in/i })
      .or(this.page.locator('#submit'))

    this.pageTitle = this.page
      .getByRole('heading', { level: 2 })
      .or(this.page.locator('h2'))

    // Success page locators
    this.successMessage = this.page.locator('.post-title')
    this.logoutButton = this.page
      .getByRole('link', { name: /log out/i })
      .or(this.page.locator('a:has-text("Log out")'))

    this.successPageTitle = this.page
      .getByRole('heading', { level: 1 })
      .or(this.page.locator('h1'))

    // Error page locators
    this.errorMessage = this.page
      .getByRole('alert')
      .or(this.page.locator('#error'))
  }

  /**
   * Navigate to login page and wait for it to load
   */
  public async navigateToLoginPage(): Promise<void> {
    await this.navigateTo(LoginPage.LOGIN_PATH)
    await this.waitForPageLoad()
    await this.validateLoginPageLoaded()
  }

  /**
   * Alternative explicit navigation method
   */
  public async goTo(): Promise<void> {
    await this.navigateToLoginPage()
  }

  /**
   * Enter username
   */
  public async enterUsername(username: string): Promise<void> {
    await this.waitForElement(this.usernameInput)
    await this.fillInput(this.usernameInput, username)
  }

  /**
   * Enter password
   */
  public async enterPassword(password: string): Promise<void> {
    await this.waitForElement(this.passwordInput)
    await this.fillInput(this.passwordInput, password)
  }

  /**
   * Fill both username and password
   */
  public async fillCredentials(username: string, password: string): Promise<void> {
    await this.enterUsername(username)
    await this.enterPassword(password)
  }

  /**
   * Click submit button
   */
  public async clickSubmit(): Promise<void> {
    await this.waitForElement(this.submitButton)
    await this.clickElement(this.submitButton)
  }

  /**
   * Perform complete login flow
   */
  public async login(username: string, password: string): Promise<void> {
    await this.fillCredentials(username, password)
    await this.clickSubmit()
  }

  // Validation methods

  /**
   * Validate login page is loaded
   */
  public async validateLoginPageLoaded(): Promise<void> {
    await this.assertElementVisible(this.usernameInput, LoginPage.ASSERT.usernameVisible)
    await this.assertElementVisible(this.passwordInput, LoginPage.ASSERT.passwordVisible)
    await this.assertElementVisible(this.submitButton, LoginPage.ASSERT.submitVisible)
    await this.assertUrlContains(LoginPage.LOGIN_PATH, LoginPage.ASSERT.onLoginPage)
  }

  /**
   * Validate failed login state
   */
  public async validateFailedLogin(): Promise<void> {
    await this.assertElementVisible(this.errorMessage, LoginPage.ASSERT.errorVisible)
    await this.assertElementContainsText(
      this.errorMessage,
      LoginPage.MSG_INVALID_USERNAME,
      LoginPage.ASSERT.errorContainsInvalidUser
    )
  }

  /**
   * Validate successful login
   */
  public async validateSuccessfulLogin(): Promise<void> {
    await this.assertUrlContains(LoginPage.SUCCESS_PATH, LoginPage.ASSERT.onSuccessPage)
    await this.assertElementVisible(this.successMessage, LoginPage.ASSERT.successVisible)
    await this.assertElementContainsText(
      this.successMessage,
      LoginPage.MSG_SUCCESS_TITLE,
      LoginPage.ASSERT.successContainsTitle
    )
    await this.assertElementVisible(this.logoutButton, LoginPage.ASSERT.logoutVisible)
  }

  // Success page methods

  /**
   * Wait until the success page is reached
   */
  public async waitForSuccessPage(timeout = 10000): Promise<void> {
    await this.waitForUrlToContain(LoginPage.SUCCESS_PATH, timeout)
  }

  /**
   * Check if login was successful by verifying URL
   */
  public async isLoginSuccessful(): Promise<boolean> {
    try {
      await this.waitForSuccessPage()
      return true
    } catch {
      return false
    }
  }

  /**
   * Get success message text
   */
  public async getSuccessMessage(): Promise<string> {
    await this.waitForElement(this.successMessage)
    return await this.getElementText(this.successMessage)
  }

  /**
   * Check if logout button is visible
   */
  public async isLogoutButtonVisible(): Promise<boolean> {
    return await this.isElementVisible(this.logoutButton)
  }

  /**
   * Wait for error message after a failed login attempt
   */
  public async waitForErrorMessage(timeout = 10000): Promise<void> {
    await this.waitForElement(this.errorMessage, { timeout, state: 'visible' })
  }

  /**
   * Get error message text
   */
  public async getErrorMessage(): Promise<string> {
    await this.waitForErrorMessage()
    return await this.getElementText(this.errorMessage)
  }

  /**
   * Composite: perform login and assert success
   * Throws with clear message if the success state is not reached.
   */
  public async loginAndAssertSuccess(username: string, password: string): Promise<void> {
    await this.login(username, password)
    await this.validateSuccessfulLogin()
  }

  /**
   * Composite: perform login and expect failure
   * Optionally accepts a custom expected message.
   */
  public async loginAndExpectFailure(
    username: string,
    password: string,
    expectedMessage: string = LoginPage.MSG_INVALID_USERNAME
  ): Promise<void> {
    await this.login(username, password)
    await this.waitForErrorMessage()
    await this.assertElementContainsText(this.errorMessage, expectedMessage, LoginPage.ASSERT.errorContainsInvalidUser)
  }
}
